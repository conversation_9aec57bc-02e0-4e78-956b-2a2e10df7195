package certif

import (
	_ "fmt"
	"log"

	_ "github.com/joho/godotenv"

	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/crypto/bcrypt"
)

type hash string

func Genhash(password string) (hash, error) {
	hashpass, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Println("Can't gen hash from password :", err)
		return "error occured", err
	}
	return hash(hashpass), nil
}

func PasswordFromDatabass(uid int) (string, error) {
	//DbConnection, err := sql.Open("sqlite3", userdb)
	return "草", nil
}

// func main() {
// 	fmt.Println(Genhash("password"))
// }
